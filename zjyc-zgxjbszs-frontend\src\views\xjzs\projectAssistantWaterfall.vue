<template>
  <basic-container>
    <!-- 返回首页按钮 -->
    <div class="back-to-home">
      <el-button type="primary" icon="el-icon-back" @click="goToHome">返回首页</el-button>
    </div>
    <div class="waterfall-container">
      <div class="waterfall-layout">
        <!-- 左侧步骤条 -->
        <el-affix :offset="affixOffset">
          <div class="steps-sidebar">
            <el-steps
              :active="activeStep"
              direction="vertical"
              finish-status="success"
              process-status="process"
              class="vertical-steps">
              <el-step
                title="选择项目"
                description="选择需要编制限价的项目"
                @click="scrollToStep(1)">
              </el-step>
              <el-step
                title="引擎计算"
                description="智能计算分析"
                @click="scrollToStep(2)">
              </el-step>
              <el-step
                title="生成报告"
                description="生成最终限价报告"
                @click="scrollToStep(3)">
              </el-step>
            </el-steps>
          </div>
        </el-affix>
        <!-- 右侧内容区域 -->
        <div class="content-area" ref="contentArea">
          <div class="assistant-header">
            <h2>编制项目最高限价</h2>
          </div>
          <!-- 步骤1：选择项目 -->
          <div class="step-section" :id="'step-1'" ref="step1">
            <div class="step-card" :class="{ 'active': activeStep === 0, 'completed': activeStep > 0, 'next': false }">
              <div class="step-header">
                <div class="header-content">
                  <div class="step-number">1</div>
                  <div class="step-info">
                    <h3>选择项目</h3>
                    <p class="step-description">请选择需要编制最高限价的项目</p>
                  </div>
                  <div class="step-status">
                    <i v-if="activeStep > 0" class="el-icon-check text-green-500"></i>
                    <i v-else-if="activeStep === 0" class="el-icon-edit text-blue-500"></i>
                  </div>
                </div>
              </div>
              <div class="step-content">
                <ProjectSelectStep
                  :formData="formData"
                  :isDetailMode="isDetailMode"
                  :isReportGenerated="isReportGenerated"
                  @next-step="handleNextStep(0)" />
              </div>
            </div>
          </div>

          <!-- 步骤2：引擎计算 -->
          <div class="step-section" :id="'step-2'" ref="step2">
            <div class="step-card" :class="{ 'active': activeStep === 1, 'completed': activeStep > 1, 'next': activeStep === 0, 'disabled': activeStep < 1 }">
              <div class="step-header">
                <div class="header-content">
                  <div class="step-number">2</div>
                  <div class="step-info">
                    <h3>引擎计算</h3>
                    <p class="step-description">智能分析计算项目限价</p>
                  </div>
                  <div class="step-status">
                    <i v-if="activeStep > 1" class="el-icon-check text-green-500"></i>
                    <i v-else-if="activeStep === 1" class="el-icon-edit text-blue-500"></i>
                    <i v-else class="el-icon-time text-gray-400"></i>
                  </div>
                </div>
              </div>
              <div class="step-content">
                <div v-if="activeStep >= 1">
                  <EngineCalculationStep
                    :formData="formData"
                    :isDetailMode="isDetailMode"
                    @next-step="handleNextStep(1)"
                  />
                </div>
                <div v-else class="skeleton-content">
                  <el-skeleton :rows="6" animated />
                  <div class="skeleton-placeholder">
                    <p class="text-gray-400">请先完成项目选择</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 步骤3：生成报告 -->
          <div class="step-section" :id="'step-3'" ref="step3">
            <div class="step-card" :class="{ 'active': activeStep === 2, 'completed': activeStep > 2, 'next': activeStep === 1, 'disabled': activeStep < 2 }">
              <div class="step-header">
                <div class="header-content">
                  <div class="step-number">3</div>
                  <div class="step-info">
                    <h3>生成报告</h3>
                    <p class="step-description">生成最终的项目最高限价报告</p>
                  </div>
                  <div class="step-status">
                    <i v-if="activeStep > 2" class="el-icon-check text-green-500"></i>
                    <i v-else-if="activeStep === 2" class="el-icon-edit text-blue-500"></i>
                    <i v-else class="el-icon-time text-gray-400"></i>
                  </div>
                </div>
              </div>
              <div class="step-content">
                <div v-if="activeStep >= 2">
                  <GenerateReportStep
                    :formData="formData"
                    :isDetailMode="isDetailMode"
                    @prev-step="handlePrevStep(2)"
                    @finish="finishProcess"
                    @report-generated="handleReportGenerated"
                  />
                </div>
                <div v-else class="skeleton-content">
                  <el-skeleton :rows="5" animated />
                  <div class="skeleton-placeholder">
                    <p class="text-gray-400">请先完成引擎计算</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </basic-container>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import ProjectSelectStep from './components/ProjectSelectStep.vue';
import EngineCalculationStep from './components/EngineCalculationStep.vue';
import GenerateReportStep from './components/GenerateReportStep.vue';
import { getDetail, getPage} from '@/api/xjzs/project';
import { getProjectReport } from '@/api/xjzs/projectReport';

export default {
  name: 'ProjectAssistantWaterfall',
  components: {
    ProjectSelectStep,
    EngineCalculationStep,
    GenerateReportStep
  },
  setup() {
    const activeStep = ref(0);
    const route = useRoute();
    const store = useStore();
    const isDetailMode = ref(false);
    const isReportGenerated = ref(false); // 添加报告生成状态
    const contentArea = ref(null);

    const router = useRouter();

    const goToHome = () => {
      router.push('/');
    };

    
    const formData = reactive({
      selectedProject: '',
      projectName: '',
      id: '',
      projectType: '',
      projectCategory: '',
      projectDescription: '',
      projectFiles: [],
      procurementMethod: '',
      algorithmCategory: '',
      calculationMethod: '',
      projectStatus: 0, // 添加项目状态字段
      standardTableRows: [],
      engineeringTableRows: [],
      trainingTableRows: [],
      calculationResult: null,
      reportData: null
    });

    // 滚动到指定步骤
    const scrollToStep = (step) => {
      if (step <= activeStep.value + 1) {
        const stepElement = document.getElementById(`step-${step}`);
        if (stepElement) {
          stepElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    };

    // 处理下一步
    const handleNextStep = (currentStep) => {
      // 验证当前步骤
      if (currentStep === 0 && !formData.selectedProject) {
        ElMessage.warning('请选择一个项目');
        return;
      }

      // 第一步完成后，自动设置计算方式
      if (currentStep === 0) {
        setCalculationMethod();
      }

      if (activeStep.value < 2) {
        activeStep.value += 1;
        ElMessage.success(`已进入步骤${activeStep.value + 1}`);
        nextTick(() => {
          scrollToStep(activeStep.value + 1);
        });
      }
    };

    // 处理上一步
    const handlePrevStep = () => {
      if (activeStep.value > 0) {
        activeStep.value -= 1;
        ElMessage.info(`返回到步骤${activeStep.value + 1}`);
        nextTick(() => {
          scrollToStep(activeStep.value + 1);
        });
      }
    };

    const finishProcess = () => {
      ElMessage.success('项目最高限价编制完成！');
      activeStep.value = 0;
      scrollToStep(1);
    };

    // 处理报告生成完成事件
    const handleReportGenerated = () => {
      isReportGenerated.value = true;
    };

    // 根据算法类型自动设置计算方式
    const setCalculationMethod = () => {
      const algorithmCategory = formData.algorithmCategory;
      if(formData.projectStatus == 1) {
        formData.calculationMethod = 'supplierInquiry';
      } else if (algorithmCategory === '培训类' || algorithmCategory === '工程咨询类') {
        formData.calculationMethod = 'government';
      } else if (algorithmCategory) {
        formData.calculationMethod = 'market';
      }
    };

    // 监听算法类型变化，自动设置计算方式
    watch(() => formData.algorithmCategory, (newVal) => {
      if (newVal && !formData.calculationMethod) {
        setCalculationMethod();
      }
    });

    // 项目选择变更时更新表单数据
    const handleProjectChange = (projectId) => {
      if (!projectId) return;

      // 这里可以根据需要添加项目选择逻辑
      // 由于这是瀑布流版本，主要关注布局和交互
      formData.id = projectId;
      formData.selectedProject = projectId;
    };

    onMounted(async () => {
//      // 进入页面时自动收起左侧菜单
//      const isCollapse = store.getters.isCollapse;
//      if (!isCollapse) {
//        store.commit('SET_COLLAPSE');
//      }

      // 检查URL参数
      const { projectId, step, mode } = route.query;
      
      if (projectId) {
        formData.id = projectId;
        formData.selectedProject = projectId;
        
        if (mode === 'detail') {
          isDetailMode.value = true;
          
          try {
            const projectData = await getPage(1, 1, {id: projectId}).then(res => {
              const data = res.data.data;
              return data.records[0];
            });

            if (projectData) {
              formData.projectName = projectData.name;
              formData.projectCode = projectData.code || `P${projectData.id}`;
              formData.projectType = projectData.type || '';
              formData.projectCategory = projectData.category || '';
              formData.projectDescription = projectData.content || '';
              formData.procurementMethod = projectData.procurementMethod || '';
              formData.algorithmCategory = projectData.algorithmCategory || '';
              formData.projectStatus = projectData.projectStatus || 0; // 添加项目状态
              formData.isSupplierInquiry = projectData.isSupplierInquiry || 0; // 是否询价项目
              formData.id = projectData.id;
              
              const reportRes = await getProjectReport(projectId);
              if (reportRes.data && reportRes.data.success) {
                const reportData = reportRes.data.data;
                formData.reportData = reportData;
                // 如果有报告数据，说明报告已生成
                isReportGenerated.value = true;

                // 直接从接口返回的数据中提取表格数据
                if (reportData.formData) {
                  // 直接使用接口返回的formData
                  const apiFormData = reportData.formData;

                  formData.calculationMethod = apiFormData.calculationMethod || formData.calculationMethod;
                  formData.calculationResult = apiFormData.calculationResult || null;

                  // 直接赋值表格数据
                  if (formData.algorithmCategory === '培训类' && apiFormData.trainingTableRows) {
                    formData.trainingTableRows = apiFormData.trainingTableRows;
                  } else if (formData.algorithmCategory === '工程咨询类' && apiFormData.engineeringTableRows) {
                    formData.engineeringTableRows = apiFormData.engineeringTableRows;
                  } else  {
                    formData.standardTableRows = apiFormData.standardTableRows;
                    formData.jdTableRows = apiFormData.jdTableRows;
                  }
                }

                // 如果有reportContent字段，也要处理
                if (reportData.reportContent) {
                  try {
                    const reportContent = JSON.parse(reportData.reportContent);

                    // 从报告内容中提取数据
                    if (reportContent.formData) {
                      formData.calculationMethod = reportContent.formData.calculationMethod || formData.calculationMethod;
                      formData.calculationResult = reportContent.formData.calculationResult || formData.calculationResult;

                      if (formData.algorithmCategory === '培训类' && reportContent.formData.trainingTableRows) {
                        formData.trainingTableRows = reportContent.formData.trainingTableRows;
                      } else if (formData.algorithmCategory === '工程咨询类' && reportContent.formData.engineeringTableRows) {
                        formData.engineeringTableRows = reportContent.formData.engineeringTableRows;
                      } else {
                        formData.standardTableRows = reportContent.formData.standardTableRows;
                        formData.jdTableRows = reportContent.formData.jdTableRows;
                      }
                    }

                    // 如果reportContent直接包含这些字段，也要处理
                    if (reportContent.calculatedPrice && !formData.calculationResult) {
                      formData.calculationResult = {
                        totalCost: reportContent.calculatedPrice,
                        success: true
                      };
                    }
                  } catch (error) {
                    console.error('解析报告内容失败:', error);
                  }
                }

                // 在详情模式下，如果有报告数据，设置所有步骤为完成状态并跳转到最后一步
                if (mode === 'detail' && !step) {
                  // 设置为最后一步，表示所有前面的步骤都已完成
                  activeStep.value = 3; // 跳转到生成报告步骤

                  // 确保前面的步骤数据也是完整的
                  if (!formData.calculationResult && reportData.calculatedPrice) {
                    formData.calculationResult = {
                      totalCost: reportData.calculatedPrice,
                      success: true
                    };
                  }
                }
              }
            }
          } catch (error) {
            console.error('获取项目详情失败:', error);
            ElMessage.error('获取项目详情失败: ' + (error.message || '未知错误'));
          }
        } else {
          handleProjectChange(projectId);
        }

        if (step && !isNaN(parseInt(step))) {
          // 调整步骤编号，从1开始的步骤转换为从0开始
          const stepNum = parseInt(step);
          if (stepNum <= 3) {
            activeStep.value = stepNum - 1;
          }
        }
      }
    });

    // 计算垂直居中的偏移量
    const affixOffset = ref(0);

    const calculateAffixOffset = () => {
      // 计算让侧边栏垂直居中的偏移量
      // 视口高度的50% - 侧边栏高度的50%
      const viewportHeight = window.innerHeight;
      const sidebarHeight = 400; // 侧边栏的大概高度
      affixOffset.value = Math.max(0, (viewportHeight - sidebarHeight) / 2);
    };

    onMounted(() => {
      calculateAffixOffset();
      // 监听窗口大小变化，重新计算偏移量
      window.addEventListener('resize', calculateAffixOffset);
    });

    onUnmounted(() => {
      window.removeEventListener('resize', calculateAffixOffset);
    });

    return {
      activeStep,
      formData,
      isDetailMode,
      isReportGenerated,
      contentArea,
      affixOffset,
      scrollToStep,
      handleNextStep,
      handlePrevStep,
      finishProcess,
      setCalculationMethod,
      handleReportGenerated,
      goToHome
    };
  }
}
</script>

<style lang="scss" scoped>

.back-to-home {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}


.waterfall-container {
  padding: 20px;
  background-color: #ffffff;
  min-height: calc(100vh - 120px);
}

.assistant-header {
  margin-bottom: 30px;
  text-align: center;
}

.assistant-header h2 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 8px;
  font-weight: 600;
}

.subtitle {
  color: #606266;
  font-size: 16px;
  margin-bottom: 15px;
}



.waterfall-layout {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.steps-sidebar {
  width: 190px;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px 10px;
  //box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
  min-height: 400px;
}

.vertical-steps {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;

  :deep(.el-steps) {
    height: 100%;
  }

  :deep(.el-step) {
    margin-bottom: 0px;
    padding-bottom: 30px;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }

  :deep(.el-step__head) {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.1);
    }
  }

  :deep(.el-step__title) {
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.3s;
    line-height: 1.5;

    &:hover {
      color: #409eff;
    }
  }

  :deep(.el-step__description) {
    font-size: 13px;
    color: #909399;
    margin-top: 8px;
    cursor: pointer;
    line-height: 1.4;
  }

  :deep(.el-step__main) {
    margin-left: 16px;
    padding-bottom: 40px;
  }

  :deep(.el-step.is-process .el-step__title) {
    color: #409eff;
    font-weight: 600;
  }

  :deep(.el-step.is-finish .el-step__title) {
    color: #67c23a;
  }

  :deep(.el-step__line) {
    height: 120px !important;
    top: 32px;
  }

  :deep(.el-step__line-inner) {
    height: 120px !important;
  }

  :deep(.el-step__icon) {
    position: relative;
    z-index: 2;
  }
}

.content-area {
  flex: 1;
  padding-right: 10px;
  width: 80%;
}

.step-section {
  margin-bottom: 20px;
  scroll-margin-top: 20px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}

.step-card {
  background-color: transparent;
  border-radius: 8px;
  padding: 0;
  border: none;
  transition: all 0.3s;
  overflow: hidden;

  &.active {
    .header-content {
      background-color: rgba(64, 158, 255, 0.05);
    }
    .step-number {
      background: #409eff;
      transform: scale(1.1);
    }
  }

  &.completed {
    .header-content {
      background-color: rgba(103, 194, 58, 0.05);
    }
    .step-number {
      background: #67c23a;
    }
  }

  &.next {
    .header-content {
      background-color: rgba(64, 158, 255, 0.03);
    }
    .step-number {
      background: #409eff;
      border: 2px solid #409eff;
      animation: pulse 2s infinite;
    }
  }

  &.disabled {
    opacity: 0.6;
    .step-number {
      background: #c0c4cc;
    }
  }
}

.step-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 30px;
  background-color: transparent;
  border-bottom: 1px solid rgba(235, 238, 245, 0.3);
  margin-bottom: 0;
  transition: all 0.3s;
  position: relative;

  /* 创建内部容器 */
  .header-content {
    display: flex;
    align-items: center;
    padding: 15px 50px;
    border-radius: 8px;
    background-color: transparent;
    transition: all 0.3s;
  }
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-right: 15px;
  flex-shrink: 0;
  transition: all 0.3s;
}

.step-info {
  flex: 0 1 auto; /* 改为根据内容自适应宽度 */
  text-align: center; /* 文字居中 */
}

.step-info h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 4px 0;
  font-weight: 600;
}

.step-description {
  color: #606266;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

.step-status {
  font-size: 20px;
  margin-left: 15px;
}

.step-content {
  padding: 15px 0 30px 0;
}

.skeleton-content {
  .skeleton-placeholder {
    text-align: center;
    padding: 20px;
    margin-top: 20px;

    p {
      font-size: 14px;
      margin: 0;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .waterfall-layout {
    flex-direction: column;
  }

  .steps-sidebar {
    width: 100%;
    margin-bottom: 20px;
    box-shadow: none;
  }

  .vertical-steps {
    :deep(.el-steps) {
      flex-direction: row;
    }
  }

  .content-area {
    max-height: none;
  }
}

@media (max-width: 768px) {
  .waterfall-container {
    padding: 15px;
  }

  .step-card {
    padding: 20px;
  }

  .step-header h3 {
    font-size: 18px;
  }
}

/* 动画效果 */
.step-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

/* 移除步骤间的连接线效果，让步骤更自然地融合 */
</style>
