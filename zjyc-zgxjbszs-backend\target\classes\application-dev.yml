#数据源配置
spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration
  data:
    redis:
      ##redis 单机环境配置
      host: 127.0.0.1
      port: 6379
      password:
      database: 10
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # MySql
#    url: *********************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: root
    # PostgreSQL
    url: *****************************************************************
    username: gzyc
    password: gzyc1234
    # Oracle
    #url: *************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # SqlServer
    #url: ********************************************************
    #username: bladex_boot
    #password: bladex_boot
    # DaMeng
    #url: jdbc:dm://127.0.0.1:5236/BLADEX_BOOT?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # YashanDB
    #url: ***************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##redis服务地址
    address: redis://127.0.0.1:6379
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://127.0.0.1:9000
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://tcinspect.foshantc.com/webfile
  #访问key
  access-key: BJEA6hXrehmRYIrGvxKk
  #密钥key
  secret-key: KiQ1NPjX5bbgQVYCGy2X19SVIErQLiQV7OKujscT
  #存储桶
  bucket-name: zsyy
#dify配置
dify:
  url: http://127.0.0.1/v1
  key:
    #京东慧采属性提取接口--搁置
    jdhc: app-JGd79ccqXgK7WiPwcYfQ1fUX
    #京东慧采单位提取接口 --搁置
    jdhcUnit: app-VvVa6neq92JvbmolO1ZUjjLm
    #培训类合规检查接口--搁置
    peixunhegui: app-PcfW81jJJJYlhRbZjHZ09QSo
   
    #培训类AgentApiKey
    training: app-2d9mnVT4r1g16F5KAeMknZUc
    #货物类AgentApiKey
    goods: app-jimAmyz3ifsRVAt5FqExPubw
    #工程类AgentApiKey
    engineering: app-RtnXdDJoaHVKJXcJU033LUT3
    #供应商询价AgentApiKey
    inquiry: app-kulXmCk1q0ZCPP10qlFuaqYV

    #培训类合规性检查AgentApiKey
    compliance: app-SsD6Pg3M6vhVdMdEubmxGQGA
    #工程类合规检查AgentApiKey
    engineeringCompliance: app-rc8af0Ke2wtJ5oDdtJfk5kFR
    #采购类合规检查AgentApiKey
    procurementCompliance: app-R6y7v89Q0A5OrUoiom7aflKo